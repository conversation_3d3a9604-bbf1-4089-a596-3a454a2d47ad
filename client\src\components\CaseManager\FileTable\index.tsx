import './index.scss';
import { Blur, DeleteX, EditAttributes, FileNullState } from '@assets/icons';
import EmptyState from '@components/CaseManager/EmptyState';
import Dialog from '@components/Dialog';
import FileCard from '@components/FileCard';
import MoveFileDialog from '@components/MoveFileDialog';
import { BlurSwitch } from '@components/Search/Switch';
import Table, { Action, Column, DataMap } from '@components/Table';
import { I18nTranslate } from '@i18n';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import WorkOutlineOutlinedIcon from '@mui/icons-material/WorkOutlineOutlined';
import { Box, Checkbox, CircularProgress, Tooltip } from '@mui/material';
import { VFile } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import { handleFileCardArrowKeys } from '@utils/helpers/handleFileCardArrowKeys';

import {
  pollFiles,
  searchFolders,
  selectBlur,
  selectDestinationCase,
  selectLimit,
  selectOffset,
  selectPendingFiles,
  selectSelectedFiles,
  selectShowMoveFileDialog,
  selectSortBy,
  selectSortDirection,
  selectViewType,
  setDestinationCase,
  setLimit,
  setOffset,
  setSelectedFileId,
  setSelectedFiles,
  setSortBy,
  setSortDirection,
  syncPendingFiles,
  toggleBlur,
  toggleMoveFileDialog,
} from '@store/modules/caseDetail/slice';
import {
  deleteFile,
  selectCaseData,
  selectRootFolderId,
} from '@store/modules/caseManager/slice';
import {
  editMetadata,
  toggleOpenEditDrawer,
} from '@store/modules/metadata/slice';
import { convertFileType, FILE_TYPE_ICON_MAP } from '@utils/files';
import { getThumbnailUrl } from '@utils/getThumbnails';
import { dispatchCustomEvent, voidWrapper } from '@utils/helpers';
import { ViewType } from '@utils/local-storage';
import { savePendingDeleteFileToLocalStorage } from '@utils/saveToLocalStorage';
import { map } from 'p-iteration';
import { ChangeEvent, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { ArrayOrSingle } from 'ts-essentials';
import {
  FileSearchParams,
  resetSearchToDefault,
  searchFiles,
  selectSearchFiles,
} from '@store/modules/search/slice.ts';
import { mapSearchToResult } from '@components/Search';

export enum OrderBy {
  FILE_TYPE = 'fileType',
  FILE_NAME = 'veritone-file.filename',
  UPLOAD_DATE = 'createdTime',
}

interface Props {
  selected: string;
  handleSelect: (selectedId: string) => void;
  handleDoubleClick: (tdoId: string) => void;
  handleUploadFile: () => void;
  setSelectedFolderId: (id: string) => void;
  selectedFolderId: string;
  setSelected: (id: string) => void;
  pendingDeleteIds: string[];
  setPendingDeleteIds: (ids: string[]) => void;
  pendingMoveFileIds: {
    fileId: string;
    newFolderId: string;
    oldFolderId: string;
  }[];
  classname?: string;
}

const TABLE_MIN_WIDTH = '1031px';

const FileTable = ({
  selected,
  handleSelect,
  handleDoubleClick,
  handleUploadFile,
  setSelectedFolderId,
  selectedFolderId,
  setSelected,
  pendingDeleteIds,
  setPendingDeleteIds,
  pendingMoveFileIds,
  classname,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const searchFilesState = useSelector(selectSearchFiles);
  const status = searchFilesState.ungroupedSearch.status;
  const searchResultsFiles: VFile[] = mapSearchToResult(
    searchFilesState.ungroupedSearch.data
  );
  const totalFiles =
    searchFilesState.ungroupedSearch.data?.searchMedia.jsondata.totalResults
      .value || 0;

  const pendingFiles = useSelector(selectPendingFiles);
  const destinationCase = useSelector(selectDestinationCase);
  const [lastSelected, setLastSelected] = useState<number | null>(null);

  const offset = useSelector(selectOffset);
  const limit = useSelector(selectLimit);
  const sortBy = useSelector(selectSortBy);
  const sortDirection = useSelector(selectSortDirection);
  const selectedFiles = useSelector(selectSelectedFiles);
  const blurred = useSelector(selectBlur);
  const viewType = useSelector(selectViewType);
  const selectedCase = useSelector(selectCaseData);
  const showMoveFileDialog = useSelector(selectShowMoveFileDialog);

  const selectedFilesSet = new Set(selectedFiles);
  const rootFolderId = useSelector(selectRootFolderId);

  const getFileSearchParams = (folderId: string): FileSearchParams => ({
    folderIdsFilter: [folderId],
    searchResultType: 'ungrouped',
    checkedResultCategories: [],
    pagination: { ungrouped: { offset: offset, limit: limit } },
    sort: { type: sortBy, order: sortDirection },
  });

  const fileMap = useMemo(
    () =>
      searchResultsFiles.reduce((acc: DataMap<VFile>, item, index) => {
        acc[item.id] = { index, item };
        return acc;
      }, {}),
    [searchResultsFiles]
  );

  const isCheckAll = useMemo(
    () =>
      searchResultsFiles.length > 0 &&
      searchResultsFiles.every(({ id }) => selectedFilesSet.has(id)),
    [searchResultsFiles, selectedFilesSet]
  );
  const isFileLoading = status === 'loading';

  const [
    openSelectedFilesDeletionConfirmationDialog,
    setOpenSelectedFilesDeletionConfirmationDialog,
  ] = useState(false);

  const updateSelectedFiles = (rowIds: string[]) => {
    dispatch(setSelectedFiles(rowIds));
  };

  const [
    openFilesDeletionConfirmationDialog,
    setOpenFilesDeletionConfirmationDialog,
  ] = useState(false);
  const pollPromiseRef = useRef<DispatchPromise>(null);

  useEffect(() => {
    dispatchCustomEvent('enable-bulk-actions', {
      isEnabled: selectedFiles.length > 0,
    });
  }, [selectedFiles]);

  // Reset search state when component unmounts
  useEffect(
    () => () => {
      dispatch(resetSearchToDefault());
    },
    []
  );

  const handleCheckAll = () => {
    const rowIds = searchResultsFiles.map(({ id }) => id);
    let newSelectedFiles: string[] = [];

    if (!isCheckAll) {
      newSelectedFiles = selectedFiles.concat(
        rowIds.filter((id) => !selectedFilesSet.has(id))
      );
    } else {
      const rowIdSet = new Set(rowIds);
      newSelectedFiles = selectedFiles.filter((id) => !rowIdSet.has(id));
    }

    updateSelectedFiles(newSelectedFiles);
    setLastSelected(null);
  };

  const handleSlice = (start: number, end: number) =>
    selectedFiles.concat(
      searchResultsFiles
        .slice(start, end + 1)
        .map(({ id }) => id)
        .filter((id) => !selectedFilesSet.has(id))
    );

  const handleHoldShift = (id: string, lastIndex: number) => {
    const index = fileMap[id].index;
    return lastIndex > index
      ? handleSlice(index, lastIndex)
      : handleSlice(lastIndex, index);
  };

  const handleChangeCheckbox = (
    id: string,
    checked: boolean,
    e: ChangeEvent<HTMLInputElement>
  ) => {
    if (!fileMap[id]) {
      return;
    }

    const nativeEvent = e.nativeEvent;
    let newSelectedFiles: string[] = [];
    if (checked) {
      if (
        lastSelected !== null &&
        nativeEvent instanceof MouseEvent &&
        nativeEvent.shiftKey
      ) {
        newSelectedFiles = handleHoldShift(id, lastSelected);
      } else {
        newSelectedFiles = selectedFiles.concat([id]);
        const index = fileMap[id].index;
        setLastSelected(index);
      }
    } else {
      newSelectedFiles = selectedFiles.filter(
        (selectedId) => selectedId !== id
      );
      setLastSelected(null);
    }

    updateSelectedFiles(newSelectedFiles);
  };

  const handleCtrlRowClick = (id: string) => {
    if (!fileMap[id]) {
      return;
    }

    let newSelectedFiles: string[] = [];

    if (selectedFilesSet.has(id)) {
      newSelectedFiles = selectedFiles.filter(
        (selectedId) => selectedId !== id
      );
      setLastSelected(null);
    } else {
      newSelectedFiles = selectedFiles.concat([id]);
      setLastSelected(fileMap[id].index);
    }
    updateSelectedFiles(newSelectedFiles);
  };

  const handleShiftRowClick = (id: string) => {
    if (lastSelected !== null) {
      const newSelecteFiles = handleHoldShift(id, lastSelected);
      updateSelectedFiles(newSelecteFiles);
    }
  };

  useEffect(() => {
    dispatch(syncPendingFiles({ folderId: selectedFolderId }));
    updateSelectedFiles([]);

    if (selectedFolderId) {
      const fileSearchParams = getFileSearchParams(selectedFolderId);
      pollPromiseRef.current = dispatch(pollFiles(fileSearchParams));
    }

    return () => {
      if (pollPromiseRef.current) {
        pollPromiseRef.current.abort();
      }
    };
  }, [selectedFolderId]);

  const renderCell = (
    value: ArrayOrSingle<string> | undefined,
    isToolTip?: boolean
  ) => {
    const cell = (
      <div className="file-table__table-cell">
        <span>{value || intl.formatMessage({ id: 'defaultEmpty' })}</span>
      </div>
    );

    if (isToolTip) {
      return (
        <Tooltip
          title={value}
          placement="bottom-start"
          disableHoverListener={!value}
        >
          {cell}
        </Tooltip>
      );
    } else {
      return cell;
    }
  };

  const fileColumns: Column<VFile>[] = [
    {
      header: searchResultsFiles.length ? (
        <Checkbox
          data-testid="check-box__check-all"
          indeterminate={
            !isCheckAll &&
            searchResultsFiles.some((row) => selectedFilesSet.has(row.id))
          }
          checked={isCheckAll}
          onChange={(_e) => handleCheckAll()}
        />
      ) : null,
      render: ({ rowId }) =>
        rowId && (
          <Checkbox
            data-testid={`check-box__check-row-${rowId}`}
            checked={selectedFilesSet.has(rowId)}
            onChange={(e, checked) => handleChangeCheckbox(rowId, checked, e)}
            onClick={(e) => e.stopPropagation()}
            onDoubleClick={(e) => e.stopPropagation()}
          />
        ),
      width: '56px',
    },
    {
      field: 'fileType',
      header: 'Type',
      render: ({ value }) => {
        if (Array.isArray(value)) {
          return;
        }
        const IconComponent =
          FILE_TYPE_ICON_MAP[
            convertFileType(value ? String(value) : undefined)
          ];
        return (
          <div className="file-table__file-type">
            <IconComponent />
          </div>
        );
      },
      isSortable: true,
      width: '90px',
    },
    {
      field: 'fileName',
      header: 'File Name',
      render: ({ value }) =>
        renderCell(typeof value === 'number' ? String(value) : value, true),
      isSortable: true,
      width: '20%',
    },
    {
      field: 'caseId',
      header: 'Case ID',
      render: () => renderCell(selectedCase?.caseId, true),
      isSortable: true,
      width: '14%',
    },
    {
      field: 'createdTime',
      header: 'Upload Date',
      render: ({ value }) => {
        if (!value || Array.isArray(value)) {
          return;
        }
        const date = new Date(value);
        return date.toISOString().split('T')[0];
      },
      isSortable: true,
      width: '12.5%',
    },
    {
      field: 'id', // TODO: update when API return status field
      header: 'Status',
      render: () => '',
      // <Chip
      //   label="ERROR UPLOADING FILE"
      //   variant="outlined"
      //   color="error"
      //   size="small"
      //   className="status-chip"
      // />
      width: '14%',
    },
    {
      field: 'description',
      header: 'Description',
      render: ({ value }) =>
        renderCell(typeof value === 'number' ? String(value) : value),
      width: '20%',
    },
  ];

  const handleOpenMoveFile = (rowId: string) => {
    handleSelect(rowId);
    dispatch(toggleMoveFileDialog());
    dispatch(
      searchFolders({
        offset: 0,
        limit: 30,
      })
    );
  };

  const handleDispatchCurrentFile = (fileId: string) => {
    dispatch(setSelectedFileId(fileId));
  };

  const handleOpenEditMetadataDrawer = (rowId: string) => {
    handleSelect(rowId);
    dispatch(editMetadata(rowId));
    dispatch(toggleOpenEditDrawer(rowId));
  };

  const handleDelete = (rowId: string) => {
    setSelected(rowId);
    setOpenFilesDeletionConfirmationDialog(true);
  };

  const handleDeletionCancel = () => {
    setOpenFilesDeletionConfirmationDialog(false);
  };

  const handleFileDeletionConfirm = () => {
    const deleteId = selected;
    dispatch(deleteFile({ tdoId: deleteId }));

    try {
      const validCaseSdoIds = savePendingDeleteFileToLocalStorage([deleteId]);
      setPendingDeleteIds(validCaseSdoIds.map((item) => item.value));
    } catch {
      console.error('unable to save deleted file to local storage');
    }
    setSelected('');
    const searchParams = getFileSearchParams(selectedFolderId);
    dispatch(searchFiles({ params: searchParams, isCaseDetails: true }));
    setOpenFilesDeletionConfirmationDialog(false);
  };

  const fileActions: Action[] = [
    {
      action: intl.formatMessage({ id: 'move' }),
      icon: <WorkOutlineOutlinedIcon />,
      onClick: handleOpenMoveFile,
    },
    {
      action: intl.formatMessage({ id: 'editMetadata' }),
      icon: <EditAttributes />,
      onClick: handleOpenEditMetadataDrawer,
    },
    {
      action: intl.formatMessage({ id: 'delete' }),
      icon: <DeleteOutlineIcon />,
      onClick: handleDelete,
    },
  ];

  const renderEmptyState = () => (
    <div>
      <div style={{ textAlign: 'center' }}>
        {isFileLoading ? (
          <CircularProgress />
        ) : (
          <EmptyState
            imageSrc={<FileNullState className="empty-state-icon" />}
            title={I18nTranslate.TranslateMessage('noFilesFound')}
            description={I18nTranslate.TranslateMessage(
              'noFilesFoundDescription',
              {
                br: <br key="line-break" />,
              }
            )}
            buttonText={I18nTranslate.TranslateMessage('addFiles')}
            onClick={handleUploadFile}
          />
        )}
      </div>
    </div>
  );

  const isValidOrderBy = (value: string): value is OrderBy =>
    Object.values(OrderBy).includes(value as OrderBy);

  const handleSort = (key: string) => {
    if (sortBy === key) {
      dispatch(setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc'));
      setSelected('');
    } else if (isValidOrderBy(key)) {
      dispatch(setSortBy(key));
      setSelected('');
    }
  };

  const onPageChange = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    dispatch(setOffset(newPage * limit));
    setLastSelected(null);
    setSelected('');
  };

  const onRowsPerPageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const oldPage = Math.floor(offset / limit);
    const newLimit = Number(event.target.value);
    let newOffset = oldPage * newLimit;

    // Ensure newOffset does not exceed total results
    if (newOffset >= totalFiles) {
      newOffset = Math.floor(totalFiles / newLimit) * newLimit;
    }

    dispatch(setOffset(newOffset));
    dispatch(setLimit(newLimit));

    setLastSelected(null);
    setSelected('');
  };

  useEffect(() => {
    pollPromiseRef.current?.abort();

    if (selectedFolderId && rootFolderId) {
      const fileSearchParams = getFileSearchParams(selectedFolderId);
      dispatch(searchFiles({ params: fileSearchParams, isCaseDetails: true }));

      // Start polling
      pollPromiseRef.current = dispatch(pollFiles(fileSearchParams));
    }

    return () => {
      // Stop previous polling when component unmounts or folder/offset/limit/... change
      pollPromiseRef.current?.abort();
    };
  }, [
    selectedFolderId,
    dispatch,
    offset,
    limit,
    sortDirection,
    sortBy,
    rootFolderId,
  ]);

  useEffect(() => {
    if (destinationCase.data?.folderId) {
      // Navigate to the destination case
      navigate(`/case-manager/${destinationCase.data.folderId}`);

      // Get files of destination case, make the page look like the destination case navigation
      const fileSearchParams = getFileSearchParams(
        destinationCase.data.folderId
      );
      dispatch(searchFiles({ params: fileSearchParams, isCaseDetails: true }));

      // This causes the destination case to be loaded after move!
      // Also re-fetch destination case in Case Detail panel
      setSelectedFolderId(destinationCase.data.folderId);

      dispatch(setDestinationCase(undefined));
    }
  }, [destinationCase]);

  const handleDeleteAllSelectedFiles = () => {
    if (selectedFiles.length === 0) {
      return;
    }

    setOpenSelectedFilesDeletionConfirmationDialog(true);
  };

  const handleMoveAllSelectedFiles = () => {
    if (selectedFiles.length === 0) {
      return;
    }

    handleSelect(selectedFiles[0]);

    dispatch(
      searchFolders({
        offset: 0,
        limit: 30,
      })
    );
    dispatch(
      toggleMoveFileDialog({
        isBulk: true,
        moveFileDialogBulkFileIds: selectedFiles,
      })
    );
  };

  const handleCheckedDeletionCancel = () => {
    setOpenSelectedFilesDeletionConfirmationDialog(false);
  };

  const handleCheckedFileDeletionConfirm = async () => {
    await map(selectedFiles, (deleteId) => {
      dispatch(deleteFile({ tdoId: deleteId }));

      try {
        const validCaseSdoIds = savePendingDeleteFileToLocalStorage([deleteId]);
        setPendingDeleteIds(validCaseSdoIds.map((item) => item.value));
      } catch {
        console.error('unable to save deleted file to local storage');
      }
      dispatch(setSelectedFiles([]));
    });
    const searchParams = getFileSearchParams(selectedFolderId);
    dispatch(searchFiles({ params: searchParams, isCaseDetails: true }));
    setOpenSelectedFilesDeletionConfirmationDialog(false);
  };

  const handleKeyUp = (e: KeyboardEvent) =>
    handleFileCardArrowKeys(e, searchResultsFiles);

  useEffect(() => {
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [searchResultsFiles]);

  useEffect(() => {
    window.addEventListener(
      'delete-all-selected-files',
      handleDeleteAllSelectedFiles
    );
    window.addEventListener(
      'move-all-selected-files',
      handleMoveAllSelectedFiles
    );

    return () => {
      window.removeEventListener(
        'delete-all-selected-files',
        handleDeleteAllSelectedFiles
      );
      window.removeEventListener(
        'move-all-selected-files',
        handleMoveAllSelectedFiles
      );
    };
  });

  return (
    <>
      {viewType === ViewType.GRID && (
        <Box className="file-table__grid-view-header">
          <Checkbox
            onClick={(e) => e.stopPropagation()}
            onChange={handleCheckAll}
            checked={isCheckAll}
          />
          <div className="file-table__blur-images-wrapper">
            <span className="file-table__blur-label">
              {intl.formatMessage({ id: 'blurImages' })}
            </span>
            <BlurSwitch
              checked={blurred}
              icon={<Blur />}
              checkedIcon={<Blur />}
              onChange={() => dispatch(toggleBlur())}
              data-testid="blur-switch"
            />
          </div>
        </Box>
      )}
      {viewType === ViewType.GRID && (
        <Box
          gap={3}
          flex={1}
          display="flex"
          flexWrap="wrap"
          justifyContent="center"
          className="file-table__card-view-content table-loader"
        >
          {isFileLoading ? (
            <CircularProgress data-testid="file-table__card-view-loading" />
          ) : searchResultsFiles.length ? (
            searchResultsFiles.map((row, index) => {
              const thumbnailUrl = getThumbnailUrl(
                row.fileType,
                row.programLiveImage || ''
              );
              return (
                <FileCard
                  index={index}
                  fileId={row.id}
                  key={row.id}
                  fileName={row.fileName}
                  fileType={row.fileType}
                  dateUploaded={intl.formatDate(row.createdTime)}
                  caseId={selectedCase?.caseId}
                  blurred={blurred}
                  fileDuration={row.duration ?? -1}
                  isChecked={selectedFilesSet.has(row.id)}
                  onCheck={(checked) => {
                    if (checked) {
                      updateSelectedFiles([...selectedFiles, row.id]);
                    } else {
                      updateSelectedFiles(
                        selectedFiles.filter(
                          (selectedId) => selectedId !== row.id
                        )
                      );
                    }
                  }}
                  onMove={() => handleOpenMoveFile(row.id)}
                  onOpenEditMetadataDrawer={handleOpenEditMetadataDrawer}
                  onViewFile={() => handleDoubleClick(row.id)}
                  onDelete={() => handleDelete(row.id)}
                  thumbnailUrl={thumbnailUrl}
                  isDefaultThumbnail={thumbnailUrl === row.programLiveImage}
                />
              );
            })
          ) : (
            <EmptyState
              imageSrc={<FileNullState className="empty-state-icon" />}
              title={I18nTranslate.TranslateMessage('noFilesFound')}
              description={I18nTranslate.TranslateMessage(
                'noFilesFoundDescription',
                {
                  br: <br key="line-break" />,
                }
              )}
              buttonText={I18nTranslate.TranslateMessage('addFiles')}
              onClick={handleUploadFile}
            />
          )}
        </Box>
      )}
      {viewType === ViewType.LIST && (
        <Table<VFile>
          data={searchResultsFiles}
          dataMap={fileMap}
          pendingDataCreate={pendingFiles}
          columns={fileColumns}
          row={{
            selected,
            handleSelect,
            handleDoubleClick,
            handleCtrlRowClick,
            handleShiftRowClick,
            pendingDeleteIds,
            pendingMoveFileIds,
          }}
          sort={{
            orderBy: sortBy,
            direction: sortDirection,
            handleSort,
          }}
          pagination={{
            page: Math.floor(offset / limit),
            count: totalFiles,
            rowsPerPage: limit,
            rowsPerPageOptions: [50, 100, 200, 300],
            onPageChange,
            onRowsPerPageChange,
          }}
          styles={{
            classname,
            isLoading: isFileLoading,
            emptyState: renderEmptyState(),
            isFixedTableLayout: true,
            noneCopyCell: true,
            fixedTableMinWidth: TABLE_MIN_WIDTH,
          }}
          actions={fileActions}
          extraProps={{
            dispatchCurrentFile: handleDispatchCurrentFile,
            pendingDeleteMessage: intl.formatMessage({
              id: 'filePendingDelete',
            }),
          }}
        />
      )}
      <MoveFileDialog
        open={showMoveFileDialog}
        context="caseDetail"
        currentFolderId={selectedFolderId}
      />
      <Dialog
        open={openFilesDeletionConfirmationDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleDeletionCancel}
        onConfirm={handleFileDeletionConfirm}
        confirmText={intl.formatMessage({ id: 'yesDeleteFile' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        useTypeConfirmation
        disableConfirm={false}
      >
        <div>
          {I18nTranslate.TranslateMessage('deleteFilesConfirmationMsg')}
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('theFileWillBeDeleted')}
          </span>
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('everyOneInOrgWillLooseAccess')}
          </span>
        </div>
      </Dialog>
      <Dialog
        open={openSelectedFilesDeletionConfirmationDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleCheckedDeletionCancel}
        onConfirm={voidWrapper(handleCheckedFileDeletionConfirm)}
        confirmText={intl.formatMessage({ id: 'yesDeleteFiles' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        useTypeConfirmation
        disableConfirm={false}
        data-testid="delete-selected-files-dialog"
      >
        <div>
          {I18nTranslate.TranslateMessage('deleteFilesConfirmationMsg')}
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('allSelectedFilesWillBeDeleted')}
          </span>
        </div>
        <div>
          <span>
            <DeleteX />
            {I18nTranslate.TranslateMessage('everyOneInOrgWillLooseAccess')}
          </span>
        </div>
      </Dialog>
    </>
  );
};

export default FileTable;
