[{"description": "", "elements": [{"description": "", "id": "tag-settings;add-tags", "keyword": "<PERSON><PERSON><PERSON>", "line": 24, "name": "Add Tags", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 10866000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 686000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 9266000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 189000000}}, {"arguments": [], "keyword": "When ", "line": 25, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 2945000000}}, {"arguments": [], "keyword": "Then ", "line": 26, "name": "The user sees Add New \"tag\" modal", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:40"}, "result": {"status": "passed", "duration": 2564000000}}, {"arguments": [], "keyword": "When ", "line": 27, "name": "The user inputs \"tag\" name \"Tag Test Add\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:43"}, "result": {"status": "passed", "duration": 1297000000}}, {"arguments": [], "keyword": "And ", "line": 28, "name": "The user confirms by clicking the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:46"}, "result": {"status": "passed", "duration": 217000000}}, {"arguments": [], "keyword": "Then ", "line": 29, "name": "The \"1st\" row of the \"tag\" list should be updated with name \"Tag Test Add\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:49"}, "result": {"status": "passed", "duration": 636000000}}, {"arguments": [], "keyword": "And ", "line": 30, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5469000000}}], "tags": [{"name": "@e2e", "line": 23}, {"name": "@tags-settings", "line": 23}], "type": "scenario"}, {"description": "", "id": "tag-settings;add-tag-in-edit-mode", "keyword": "<PERSON><PERSON><PERSON>", "line": 33, "name": "Add tag in edit mode", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 7621000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 585000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 8884000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 127000000}}, {"arguments": [], "keyword": "When ", "line": 34, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 4841000000}}, {"arguments": [], "keyword": "Then ", "line": 35, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 20000000}}, {"arguments": [], "keyword": "When ", "line": 36, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 433000000}}, {"arguments": [], "keyword": "Then ", "line": 37, "name": "The user sees Add New \"tag\" modal", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:40"}, "result": {"status": "passed", "duration": 2486000000}}, {"arguments": [], "keyword": "When ", "line": 38, "name": "The user inputs \"tag\" name \"Tag Test Edit\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:43"}, "result": {"status": "passed", "duration": 4580000000}}, {"arguments": [], "keyword": "And ", "line": 39, "name": "The user confirms by clicking the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:46"}, "result": {"status": "passed", "duration": 625000000}}, {"arguments": [], "keyword": "When ", "line": 40, "name": "The user sees the new \"tag\" status \"Active\" for \"Tag Test Edit\" in edit mode", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:26"}, "result": {"status": "passed", "duration": 101000000}}, {"arguments": [], "keyword": "And ", "line": 41, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 429000000}}, {"arguments": [], "keyword": "Then ", "line": 42, "name": "The \"1st\" row of the \"tag\" list should be updated with name \"Tag Test Edit\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:49"}, "result": {"status": "passed", "duration": 1422000000}}, {"arguments": [], "keyword": "And ", "line": 43, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5029000000}}], "tags": [{"name": "@e2e", "line": 32}, {"name": "@tags-settings", "line": 32}], "type": "scenario"}, {"description": "", "id": "tag-settings;tag-set-individual-visibility", "keyword": "<PERSON><PERSON><PERSON>", "line": 46, "name": "Tag set individual visibility", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 7582000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 798000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 8379000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 121000000}}, {"arguments": [], "keyword": "When ", "line": 47, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 3365000000}}, {"arguments": [], "keyword": "Given ", "line": 48, "name": "The user creates a default tag name \"Tag Visibility\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2722000000}}, {"arguments": [], "keyword": "And ", "line": 49, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5509000000}}, {"arguments": [], "keyword": "When ", "line": 50, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 1193000000}}, {"arguments": [], "keyword": "Then ", "line": 51, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 30000000}}, {"arguments": [], "keyword": "When ", "line": 52, "name": "The user selects the visibility next to any status in the list and a dropdown appears", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:52"}, "result": {"status": "passed", "duration": 167000000}}, {"arguments": [], "keyword": "And ", "line": 53, "name": "The user selects \"Inactive\" visibility", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:55"}, "result": {"status": "passed", "duration": 3245000000}}, {"arguments": [], "keyword": "Then ", "line": 54, "name": "The user sees the new \"tag\" status \"Inactive\" for \"Tag Visibility\" in edit mode", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:26"}, "result": {"status": "passed", "duration": 110000000}}, {"arguments": [], "keyword": "And ", "line": 55, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 529000000}}, {"arguments": [], "keyword": "Then ", "line": 56, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6348000000}}, {"arguments": [], "keyword": "And ", "line": 57, "name": "The affected \"tag\" for \"Tag Visibility\" should have updated visibility \"Inactive\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 71000000}}, {"arguments": [], "keyword": "When ", "line": 58, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 1083000000}}, {"arguments": [], "keyword": "Then ", "line": 59, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 36000000}}, {"arguments": [], "keyword": "When ", "line": 60, "name": "The user selects the visibility next to any status in the list and a dropdown appears", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:52"}, "result": {"status": "passed", "duration": 180000000}}, {"arguments": [], "keyword": "And ", "line": 61, "name": "The user selects \"Active\" visibility", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:55"}, "result": {"status": "passed", "duration": 2835000000}}, {"arguments": [], "keyword": "Then ", "line": 62, "name": "The user sees the new \"tag\" status \"Active\" for \"Tag Visibility\" in edit mode", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:26"}, "result": {"status": "passed", "duration": 103000000}}, {"arguments": [], "keyword": "And ", "line": 63, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 349000000}}, {"arguments": [], "keyword": "Then ", "line": 64, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6071000000}}, {"arguments": [], "keyword": "And ", "line": 65, "name": "The affected \"tag\" for \"Tag Visibility\" should have updated visibility \"Active\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 93000000}}], "tags": [{"name": "@e2e", "line": 45}, {"name": "@tags-settings", "line": 45}], "type": "scenario"}, {"description": "", "id": "tag-settings;tag-change-tag-name", "keyword": "<PERSON><PERSON><PERSON>", "line": 68, "name": "Tag change Tag name", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 8169000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 692000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 9641000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 120000000}}, {"arguments": [], "keyword": "When ", "line": 69, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 2742000000}}, {"arguments": [], "keyword": "Given ", "line": 70, "name": "The user creates a default tag name \"Tag Test Name\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2163000000}}, {"arguments": [], "keyword": "And ", "line": 71, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5810000000}}, {"arguments": [], "keyword": "When ", "line": 72, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 947000000}}, {"arguments": [], "keyword": "Then ", "line": 73, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 31000000}}, {"arguments": [], "keyword": "When ", "line": 74, "name": "The user edits the new name \"Test Name Edit\" of any status in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:61"}, "result": {"status": "passed", "duration": 4867000000}}, {"arguments": [], "keyword": "Then ", "line": 75, "name": "The user sees the new \"tag\" status \"Active\" for \"Test Name Edit\" in edit mode", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:26"}, "result": {"status": "passed", "duration": 181000000}}, {"arguments": [], "keyword": "And ", "line": 76, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 599000000}}, {"arguments": [], "keyword": "Then ", "line": 77, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 8058000000}}, {"arguments": [], "keyword": "And ", "line": 78, "name": "The affected statuses for \"tag\" have updated names \"Test Name Edit\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:58"}, "result": {"status": "passed", "duration": 167000000}}], "tags": [{"name": "@e2e", "line": 67}, {"name": "@tags-settings", "line": 67}], "type": "scenario"}, {"description": "", "id": "tag-settings;tag-individual-delete", "keyword": "<PERSON><PERSON><PERSON>", "line": 81, "name": "Tag individual delete", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 14843000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 1681000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 16071000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 237000000}}, {"arguments": [], "keyword": "When ", "line": 82, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 4495000000}}, {"arguments": [], "keyword": "Given ", "line": 83, "name": "The user creates a default tag name \"Tag Test Delete\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 3836000000}}, {"arguments": [], "keyword": "And ", "line": 84, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5766000000}}, {"arguments": [], "keyword": "When ", "line": 85, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 1361000000}}, {"arguments": [], "keyword": "Then ", "line": 86, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 40000000}}, {"arguments": [], "keyword": "When ", "line": 87, "name": "The user selects the delete icon next to any status in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:64"}, "result": {"status": "passed", "duration": 1254000000}}, {"arguments": [], "keyword": "Then ", "line": 88, "name": "A confirmation dialog appears", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:67"}, "result": {"status": "passed", "duration": 1427000000}}, {"arguments": [], "keyword": "When ", "line": 89, "name": "The user confirms the deletion", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:90"}, "result": {"status": "passed", "duration": 448000000}}, {"arguments": [], "keyword": "Then ", "line": 90, "name": "The row is greyed out in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:70"}, "result": {"status": "passed", "duration": 2576000000}}, {"arguments": [], "keyword": "And ", "line": 91, "name": "The user can click <PERSON><PERSON> to discard the edits, restoring the row to its original state", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:73"}, "result": {"status": "passed", "duration": 3048000000}}, {"arguments": [], "keyword": "When ", "line": 92, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 1026000000}}, {"arguments": [], "keyword": "Then ", "line": 93, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 32000000}}, {"arguments": [], "keyword": "When ", "line": 94, "name": "The user selects the delete icon next to any status in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:64"}, "result": {"status": "passed", "duration": 380000000}}, {"arguments": [], "keyword": "Then ", "line": 95, "name": "A confirmation dialog appears", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:67"}, "result": {"status": "passed", "duration": 2257000000}}, {"arguments": [], "keyword": "When ", "line": 96, "name": "The user confirms the deletion", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:90"}, "result": {"status": "passed", "duration": 343000000}}, {"arguments": [], "keyword": "Then ", "line": 97, "name": "The row is greyed out in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:70"}, "result": {"status": "passed", "duration": 2692000000}}, {"arguments": [], "keyword": "And ", "line": 98, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 350000000}}, {"arguments": [], "keyword": "Then ", "line": 99, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6256000000}}, {"arguments": [], "keyword": "And ", "line": 100, "name": "The deleted row \"Tag Test Delete\" are permanently removed from the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:77"}, "result": {"status": "passed", "duration": 328000000}}], "tags": [{"name": "@e2e", "line": 80}, {"name": "@tag-settings", "line": 80}], "type": "scenario"}, {"description": "", "id": "tag-settings;tag-bulk-set-visibility", "keyword": "<PERSON><PERSON><PERSON>", "line": 103, "name": "Tag Bulk Set Visibility", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 9603000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 700000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 7106000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 120000000}}, {"arguments": [], "keyword": "When ", "line": 104, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 2430000000}}, {"arguments": [], "keyword": "Given ", "line": 105, "name": "The user creates a default tag name \"BulkVisibility1\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2161000000}}, {"arguments": [], "keyword": "And ", "line": 106, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5800000000}}, {"arguments": [], "keyword": "When ", "line": 107, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 222000000}}, {"arguments": [], "keyword": "Given ", "line": 108, "name": "The user creates a default tag name \"BulkVisibility2\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2818000000}}, {"arguments": [], "keyword": "And ", "line": 109, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5914000000}}, {"arguments": [], "keyword": "When ", "line": 110, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 993000000}}, {"arguments": [], "keyword": "Then ", "line": 111, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 28000000}}, {"arguments": [], "keyword": "When ", "line": 112, "name": "The user selects multiple 1 statuses using checkboxes", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:93"}, "result": {"status": "passed", "duration": 666000000}}, {"arguments": [], "keyword": "When ", "line": 113, "name": "The user clicks the Set Visibility button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:99"}, "result": {"status": "passed", "duration": 361000000}}, {"arguments": [], "keyword": "And ", "line": 114, "name": "The user selects Inactive visibility", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:102"}, "result": {"status": "passed", "duration": 2986000000}}, {"arguments": [], "keyword": "When ", "line": 115, "name": "The user confirms by clicking Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:108"}, "result": {"status": "passed", "duration": 364000000}}, {"arguments": [], "keyword": "When ", "line": 116, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 445000000}}, {"arguments": [], "keyword": "Then ", "line": 117, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6077000000}}, {"arguments": [], "keyword": "And ", "line": 118, "name": "The affected \"tag\" for \"BulkVisibility2\" should have updated visibility \"Inactive\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 75000000}}, {"arguments": [], "keyword": "When ", "line": 119, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 935000000}}, {"arguments": [], "keyword": "Then ", "line": 120, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 38000000}}, {"arguments": [], "keyword": "When ", "line": 121, "name": "The user selects multiple 2 statuses using checkboxes", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:93"}, "result": {"status": "passed", "duration": 1331000000}}, {"arguments": [], "keyword": "When ", "line": 122, "name": "The user clicks the Set Visibility button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:99"}, "result": {"status": "passed", "duration": 384000000}}, {"arguments": [], "keyword": "And ", "line": 123, "name": "The user selects Active visibility", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:105"}, "result": {"status": "passed", "duration": 1904000000}}, {"arguments": [], "keyword": "When ", "line": 124, "name": "The user confirms by clicking Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:108"}, "result": {"status": "passed", "duration": 363000000}}, {"arguments": [], "keyword": "When ", "line": 125, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 417000000}}, {"arguments": [], "keyword": "Then ", "line": 126, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6328000000}}, {"arguments": [], "keyword": "And ", "line": 127, "name": "The affected \"tag\" for \"BulkVisibility2\" should have updated visibility \"Active\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 81000000}}], "tags": [{"name": "@e2e", "line": 102}, {"name": "@tag-settings", "line": 102}], "type": "scenario"}, {"description": "", "id": "tag-settings;tag-bulk-delete", "keyword": "<PERSON><PERSON><PERSON>", "line": 130, "name": "Tag Bulk Delete", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 8008000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 606000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 7958000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 169000000}}, {"arguments": [], "keyword": "When ", "line": 131, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 2904000000}}, {"arguments": [], "keyword": "Given ", "line": 132, "name": "The user creates a default tag name \"Bulk Delete 1\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2136000000}}, {"arguments": [], "keyword": "And ", "line": 133, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5778000000}}, {"arguments": [], "keyword": "When ", "line": 134, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 190000000}}, {"arguments": [], "keyword": "Given ", "line": 135, "name": "The user creates a default tag name \"Bulk Delete 2\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2839000000}}, {"arguments": [], "keyword": "And ", "line": 136, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5817000000}}, {"arguments": [], "keyword": "When ", "line": 137, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 937000000}}, {"arguments": [], "keyword": "Then ", "line": 138, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 28000000}}, {"arguments": [], "keyword": "When ", "line": 139, "name": "The user selects multiple 1 statuses using checkboxes", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:93"}, "result": {"status": "passed", "duration": 599000000}}, {"arguments": [], "keyword": "When ", "line": 140, "name": "The user clicks the Delete button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:111"}, "result": {"status": "passed", "duration": 344000000}}, {"arguments": [], "keyword": "Then ", "line": 141, "name": "A confirmation dialog appears", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:67"}, "result": {"status": "passed", "duration": 1897000000}}, {"arguments": [], "keyword": "When ", "line": 142, "name": "The user confirms the deletion", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:90"}, "result": {"status": "passed", "duration": 563000000}}, {"arguments": [], "keyword": "Then ", "line": 143, "name": "The row is greyed out in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:70"}, "result": {"status": "passed", "duration": 2581000000}}, {"arguments": [], "keyword": "And ", "line": 144, "name": "The user can click <PERSON><PERSON> to discard the edits, restoring the row to its original state", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:73"}, "result": {"status": "passed", "duration": 2608000000}}, {"arguments": [], "keyword": "When ", "line": 145, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 897000000}}, {"arguments": [], "keyword": "Then ", "line": 146, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 34000000}}, {"arguments": [], "keyword": "When ", "line": 147, "name": "The user selects multiple 2 statuses using checkboxes", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:93"}, "result": {"status": "passed", "duration": 1201000000}}, {"arguments": [], "keyword": "When ", "line": 148, "name": "The user clicks the Delete button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:111"}, "result": {"status": "passed", "duration": 362000000}}, {"arguments": [], "keyword": "Then ", "line": 149, "name": "A confirmation dialog appears", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:67"}, "result": {"status": "passed", "duration": 2538000000}}, {"arguments": [], "keyword": "When ", "line": 150, "name": "The user confirms the deletion", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:90"}, "result": {"status": "passed", "duration": 575000000}}, {"arguments": [], "keyword": "Then ", "line": 151, "name": "The row is greyed out in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:70"}, "result": {"status": "passed", "duration": 2461000000}}, {"arguments": [], "keyword": "And ", "line": 152, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 772000000}}, {"arguments": [], "keyword": "Then ", "line": 153, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6289000000}}, {"arguments": [], "keyword": "And ", "line": 154, "name": "The deleted row \"Bulk Delete 1\" are permanently removed from the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:77"}, "result": {"status": "passed", "duration": 342000000}}, {"arguments": [], "keyword": "And ", "line": 155, "name": "The deleted row \"Bulk Delete 2\" are permanently removed from the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:77"}, "result": {"status": "passed", "duration": 334000000}}], "tags": [{"name": "@e2e", "line": 129}, {"name": "@tag-settings", "line": 129}], "type": "scenario"}, {"description": "", "id": "tag-settings;tags-reorder", "keyword": "<PERSON><PERSON><PERSON>", "line": 158, "name": "Tags reorder", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 7219000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 645000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 7243000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 125000000}}, {"arguments": [], "keyword": "When ", "line": 159, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 3006000000}}, {"arguments": [], "keyword": "Then ", "line": 160, "name": "The user sees Add New \"tag\" modal", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:40"}, "result": {"status": "passed", "duration": 2174000000}}, {"arguments": [], "keyword": "When ", "line": 161, "name": "The user inputs \"tag\" name \"Tag Reorder 1\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:43"}, "result": {"status": "passed", "duration": 1402000000}}, {"arguments": [], "keyword": "And ", "line": 162, "name": "The user confirms by clicking the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:46"}, "result": {"status": "passed", "duration": 202000000}}, {"arguments": [], "keyword": "Then ", "line": 163, "name": "The \"1st\" row of the \"tag\" list should be updated with name \"Tag Reorder 1\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:49"}, "result": {"status": "passed", "duration": 387000000}}, {"arguments": [], "keyword": "And ", "line": 164, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5497000000}}, {"arguments": [], "keyword": "When ", "line": 165, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 202000000}}, {"arguments": [], "keyword": "Then ", "line": 166, "name": "The user sees Add New \"tag\" modal", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:40"}, "result": {"status": "passed", "duration": 2804000000}}, {"arguments": [], "keyword": "When ", "line": 167, "name": "The user inputs \"tag\" name \"Tag Reorder 2\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:43"}, "result": {"status": "passed", "duration": 1426000000}}, {"arguments": [], "keyword": "And ", "line": 168, "name": "The user confirms by clicking the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:46"}, "result": {"status": "passed", "duration": 224000000}}, {"arguments": [], "keyword": "Then ", "line": 169, "name": "The \"1st\" row of the \"tag\" list should be updated with name \"Tag Reorder 2\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:49"}, "result": {"status": "passed", "duration": 310000000}}, {"arguments": [], "keyword": "And ", "line": 170, "name": "A snack notification 'Tag added successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5474000000}}, {"arguments": [], "keyword": "When ", "line": 171, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 958000000}}, {"arguments": [], "keyword": "Then ", "line": 172, "name": "A new button entitled Save Changes appears and is disabled", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:37"}, "result": {"status": "passed", "duration": 34000000}}, {"arguments": [], "keyword": "When ", "line": 173, "name": "The user drags the \"1st\" row to \"3rd\" row in the list", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:114"}, "result": {"status": "passed", "duration": 1118000000}}, {"arguments": [], "keyword": "When ", "line": 174, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 806000000}}, {"arguments": [], "keyword": "Then ", "line": 175, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6052000000}}, {"arguments": [], "keyword": "And ", "line": 176, "name": "The \"1st\" row of the \"tag\" list should be updated with name \"Tag Reorder 1\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:49"}, "result": {"status": "passed", "duration": 80000000}}, {"arguments": [], "keyword": "And ", "line": 177, "name": "The \"3rd\" row of the \"tag\" list should be updated with name \"Tag Reorder 2\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:49"}, "result": {"status": "passed", "duration": 79000000}}], "tags": [{"name": "@e2e", "line": 157}, {"name": "@tags-settings", "line": 157}], "type": "scenario"}, {"description": "", "id": "tag-settings;two-users-editing-tags", "keyword": "<PERSON><PERSON><PERSON>", "line": 180, "name": "Two users editing Tags", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 10435000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 624000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 9783000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 122000000}}, {"arguments": [], "keyword": "When ", "line": 181, "name": "The user clicks on the Add button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:8"}, "result": {"status": "passed", "duration": 3191000000}}, {"arguments": [], "keyword": "When ", "line": 182, "name": "The user creates a default tag name \"multipleEditTag\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:16"}, "result": {"status": "passed", "duration": 2432000000}}, {"arguments": [], "keyword": "Then ", "line": 183, "name": "A snack notification \"Tag added successfully\" confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 5820000000}}, {"arguments": [], "keyword": "When ", "line": 184, "name": "The user changes the visibility to \"Inactive\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:14"}, "result": {"status": "passed", "duration": 4195000000}}, {"arguments": [], "keyword": "Then ", "line": 185, "name": "The user sees the new \"tag\" status \"Inactive\" for \"multipleEditTag\" in edit mode", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:26"}, "result": {"status": "passed", "duration": 97000000}}, {"arguments": [], "keyword": "And ", "line": 186, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 396000000}}, {"arguments": [], "keyword": "Then ", "line": 187, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6281000000}}, {"arguments": [], "keyword": "When ", "line": 188, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 130000000}}, {"arguments": [], "keyword": "Then ", "line": 189, "name": "The affected \"tag\" for \"multipleEditTag\" should have updated visibility \"Inactive\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 71000000}}, {"arguments": [], "keyword": "When ", "line": 190, "name": "The user logins as \"user2\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:7"}, "result": {"status": "passed", "duration": 601000000}}, {"arguments": [], "keyword": "And ", "line": 191, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 7357000000}}, {"arguments": [], "keyword": "And ", "line": 192, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 170000000}}, {"arguments": [], "keyword": "Then ", "line": 193, "name": "The affected \"tag\" for \"multipleEditTag\" should have updated visibility \"Inactive\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 1094000000}}, {"arguments": [], "keyword": "When ", "line": 194, "name": "The user changes the visibility to \"Active\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:14"}, "result": {"status": "passed", "duration": 4332000000}}, {"arguments": [], "keyword": "Then ", "line": 195, "name": "The user sees the new \"tag\" status \"Active\" for \"multipleEditTag\" in edit mode", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:26"}, "result": {"status": "passed", "duration": 109000000}}, {"arguments": [], "keyword": "And ", "line": 196, "name": "The user clicks Save Changes button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:11"}, "result": {"status": "passed", "duration": 409000000}}, {"arguments": [], "keyword": "Then ", "line": 197, "name": "A snack notification 'Tags saved successfully' confirming this has succeeded", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:23"}, "result": {"status": "passed", "duration": 6292000000}}, {"arguments": [], "keyword": "Then ", "line": 198, "name": "The affected \"tag\" for \"multipleEditTag\" should have updated visibility \"Active\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:30"}, "result": {"status": "passed", "duration": 81000000}}], "tags": [{"name": "@e2e", "line": 179}, {"name": "@tags-settings", "line": 179}], "type": "scenario"}, {"description": "", "id": "tag-settings;keyboard-navigation-with-tab-and-arrow-keys-in-tag-table", "keyword": "<PERSON><PERSON><PERSON>", "line": 201, "name": "Keyboard navigation with TAB and arrow keys in tag table", "steps": [{"arguments": [], "keyword": "Given ", "line": 4, "name": "The user logins successfully", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:4"}, "result": {"status": "passed", "duration": 7818000000}}, {"arguments": [{"rows": [{"cells": ["tagName"]}, {"cells": ["Tag Test Add"]}, {"cells": ["Tag Test Edit"]}, {"cells": ["Tag Visibility"]}, {"cells": ["Tag Test Name"]}, {"cells": ["Test Name Edit"]}, {"cells": ["Tag Test Delete"]}, {"cells": ["BulkVisibility1"]}, {"cells": ["BulkVisibility2"]}, {"cells": ["Bulk Delete 1"]}, {"cells": ["Bulk Delete 2"]}, {"cells": ["multipleEditTag"]}, {"cells": ["Tag Reorder 1"]}, {"cells": ["Tag Reorder 2"]}]}], "keyword": "Given ", "line": 5, "name": "The user deletes tag if exists", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:13"}, "result": {"status": "passed", "duration": 570000000}}, {"arguments": [], "keyword": "And ", "line": 20, "name": "The user is on Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:5"}, "result": {"status": "passed", "duration": 8318000000}}, {"arguments": [], "keyword": "And ", "line": 21, "name": "The user is on Tag Settings screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/tag-setting/tag-setting.steps.ts:10"}, "result": {"status": "passed", "duration": 122000000}}, {"arguments": [], "keyword": "When ", "line": 202, "name": "The user clicks Edit button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:33"}, "result": {"status": "passed", "duration": 3521000000}}, {"arguments": [], "keyword": "And ", "line": 203, "name": "The user presses \"Tab\" key in settings table", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:117"}, "result": {"status": "passed", "duration": 62000000}}, {"arguments": [], "keyword": "And ", "line": 204, "name": "The user presses \"Enter\" key 1 time(s)", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common/common.step.ts:2"}, "result": {"status": "passed", "duration": 468000000}}, {"arguments": [], "keyword": "Then ", "line": 205, "name": "The \"1st\" row is selected", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:80"}, "result": {"status": "passed", "duration": 77000000}}, {"arguments": [], "keyword": "And ", "line": 206, "name": "The user presses \"ArrowDown\" key 2 time(s)", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common/common.step.ts:2"}, "result": {"status": "passed", "duration": 99000000}}, {"arguments": [], "keyword": "And ", "line": 207, "name": "The user presses \"Enter\" key 1 time(s)", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common/common.step.ts:2"}, "result": {"status": "passed", "duration": 444000000}}, {"arguments": [], "keyword": "Then ", "line": 208, "name": "The \"1st\" row is selected", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:80"}, "result": {"status": "passed", "duration": 72000000}}, {"arguments": [], "keyword": "And ", "line": 209, "name": "The \"3rd\" row is selected", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/common-setting/common-setting.ts:80"}, "result": {"status": "passed", "duration": 83000000}}], "tags": [{"name": "@e2e", "line": 200}, {"name": "@tags-settings", "line": 200}, {"name": "@keyboard-event", "line": 200}], "type": "scenario"}], "id": "tag-settings", "line": 1, "keyword": "Feature", "name": "Tag Settings", "tags": [], "uri": "cypress\\e2e\\features\\tag-setting.feature"}]