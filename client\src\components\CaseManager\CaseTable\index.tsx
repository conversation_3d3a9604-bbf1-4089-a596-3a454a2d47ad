import './index.scss';
import { FolderClosed } from '@assets/icons';
import { emptyCasesState } from '@assets/images';
import CaseFilter from '@components/CaseFilter';
import EmptyState from '@components/CaseManager/EmptyState';
import CaseStatusMenu from '@components/CaseStatus';
import Dialog from '@components/Dialog';
import Table, { Action, Column, DataMap } from '@components/Table';
import { I18nTranslate } from '@i18n';
import {
  Add as AddIcon,
  DeleteOutline as DeleteOutlineIcon,
  WorkOutlineOutlined as WorkOutlineOutlinedIcon,
} from '@mui/icons-material';
import { TableCell, TableRow, Tooltip } from '@mui/material';
import Drawer from '@mui/material/Drawer';
import { CaseFilterValue, CaseSearchResults } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  checkCases,
  deleteCase,
  pollCases,
  pollStatusesTags,
  saveCase,
  searchCases,
  selectCaseFilter,
  selectCases,
  selectFolderContentTemplateSchema,
  selectLimit,
  selectOffset,
  selectSortBy,
  selectSortDirection,
  setCaseFilter,
  setLimit,
  setOffset,
  setSort,
  toggleCaseDrawer,
} from '@store/modules/caseManager/slice';
import {
  selectEvidenceTypeSchema,
  selectStatusSchema,
  selectTagSchema,
} from '@store/modules/config/slice';
import { selectFetchedStatuses } from '@store/modules/settings/slice';
import { SortBy, voidWrapper } from '@utils/helpers';
import { getCaseStatuses } from '@utils/local-storage';
import { savePendingDeleteCaseToLocalStorage } from '@utils/saveToLocalStorage';
import dayjs from 'dayjs';
import { isEqual } from 'lodash';
import { ChangeEvent, useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';

export type Case = CaseSearchResults['searchMedia']['jsondata']['results'][0];

interface Props {
  caseMap: DataMap<Case>;
  selected: string;
  setSelected: (id: string) => void;
  handleSelect: (selectedId: string) => void;
  handleDoubleClick: () => void;
  handleUploadFile: () => void;
  pendingDeleteIds: string[];
  setPendingDeleteIds: (ids: string[]) => void;
  classname?: string;
}

const appliedCaseFiltersCount = (caseFilter: CaseFilterValue) => {
  let count = 0;
  if (caseFilter.caseName && caseFilter.caseName.length > 0) {
    count++;
  }
  if (caseFilter.caseId && caseFilter.caseId.length > 0) {
    count++;
  }
  if (caseFilter.tagIds && caseFilter.tagIds.length > 0) {
    count += caseFilter.tagIds.length;
  }
  if (caseFilter.statusId && caseFilter.statusId.length > 0) {
    count++;
  }
  return count;
};

const CaseTable = ({
  caseMap,
  selected,
  handleSelect,
  handleDoubleClick,
  pendingDeleteIds,
  setSelected,
  handleUploadFile,
  setPendingDeleteIds,
  classname,
}: Props) => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();

  const [openCaseDeleteDialog, setOpenCaseDeleteDialog] = useState(false);
  const [openCaseFilter, setOpenCaseFilter] = useState(false);
  const sortBy = useSelector(selectSortBy);
  const sortDirection = useSelector(selectSortDirection);
  const caseFilter = useSelector(selectCaseFilter);
  const offset = useSelector(selectOffset);
  const limit = useSelector(selectLimit);
  const currentStatuses = useSelector(selectFetchedStatuses);

  const folderContentTemplateSchema = useSelector(
    selectFolderContentTemplateSchema
  );
  const folderContentTemplateSchemaId = folderContentTemplateSchema.id;
  const statusSchema = useSelector(selectStatusSchema);
  const tagSchema = useSelector(selectTagSchema);
  const evidenceTypeSchema = useSelector(selectEvidenceTypeSchema);

  const {
    status: casesStatus,
    data: { results: cases, totalResults },
  } = useSelector(selectCases);

  const isLoadingError =
    casesStatus === 'failure' ||
    casesStatus === 'concurrentModificationError' ||
    statusSchema.status === 'failure' ||
    statusSchema.status === 'concurrentModificationError' ||
    tagSchema.status === 'failure' ||
    tagSchema.status === 'concurrentModificationError' ||
    evidenceTypeSchema.status === 'failure' ||
    evidenceTypeSchema.status === 'concurrentModificationError';

  const isTableLoading =
    !isLoadingError &&
    (casesStatus === 'idle' ||
      casesStatus === 'loading' ||
      statusSchema.status === 'idle' ||
      statusSchema.status === 'loading' ||
      tagSchema.status === 'idle' ||
      tagSchema.status === 'loading' ||
      evidenceTypeSchema.status === 'idle' ||
      evidenceTypeSchema.status === 'loading');

  const pollCasesPromiseRef = useRef<DispatchPromise>(null);
  const pollStatusesTagsPromiseRef = useRef<DispatchPromise>(null);

  const onSaveStatus = useCallback(
    async (statusId: string, rowId: string | undefined) => {
      if (!rowId || !caseMap[rowId]) {
        return;
      }

      const selectedCase = caseMap[rowId].item;

      pollCasesPromiseRef.current?.abort();

      await dispatch(
        saveCase({
          caseName: selectedCase.caseName,
          caseId: selectedCase.caseId,
          description: selectedCase.description,
          caseDate: selectedCase.caseDate,
          statusId,
          preconfiguredTagIds: selectedCase.preconfiguredTagIds,
          createdBy: selectedCase.createdBy,
          folderId: selectedCase.folderId,
          sdoId: selectedCase.id,
          createdDateTime: selectedCase.createdDateTime,
        })
      );
      pollCasesPromiseRef.current = dispatch(pollCases());
      // pollCasesPromiseRef.current = dispatch(pollCases(statusId)); // TODO: Check if statusId is needed
    },
    [caseMap, dispatch, selected]
  );

  const caseColumns: Column<Case>[] = [
    {
      header: '',
      render: () => <FolderClosed />,
      width: '48px',
    },
    {
      field: 'caseName',
      header: 'Case Name',
      render: ({ value }) => {
        if (Array.isArray(value)) {
          return;
        }
        return (
          <Tooltip title={value} placement={'bottom-start'}>
            <div className="case-table-cell" data-testid="row-case-name">
              <span>{value}</span>
            </div>
          </Tooltip>
        );
      },
      isSortable: true,
      width: '36%',
    },
    {
      field: 'caseId',
      header: 'Case ID',
      render: ({ value }) => {
        if (Array.isArray(value)) {
          return;
        }
        return (
          <Tooltip title={value}>
            <div className="case-table-cell">
              <span>{value}</span>
            </div>
          </Tooltip>
        );
      },
      isSortable: false,
      width: '15%',
    },
    {
      field: 'caseDate',
      header: 'Case Date',
      isSortable: true,
      width: '16%',
      render: ({ value }) =>
        value && typeof value === 'string'
          ? dayjs(value).format('MM/DD/YYYY')
          : '',
    },
    // {
    //   field: 'modifiedDateTime',
    //   header: 'Retention Date',
    //   render: ({ value }) => {
    //     if (Array.isArray(value)) {
    //       return;
    //     }
    //     const date = dayjs(value);
    //     const day = date.format('YYYY-MM-DD');
    //     const hour = date.format('h:mm A');
    //     return (
    //       <span className="retention-date">
    //         <span>{day}</span>
    //         <span>{hour}</span>
    //       </span>
    //     );
    //   },
    //   isSortable: true,
    //   width: '15%',
    // },
    {
      field: 'statusId',
      header: 'Status',
      render: ({ value, rowId }) => {
        if (Array.isArray(value) || !rowId) {
          return;
        }
        const validStatuses = getCaseStatuses();
        const cachedStatus = validStatuses.find(
          (status) => status.id === rowId
        );
        let statusId = value;
        if (cachedStatus && cachedStatus.statusId !== value) {
          statusId = cachedStatus.statusId;
        }

        return (
          <CaseStatusMenu
            currentStatusId={statusId}
            onSaveStatus={voidWrapper(onSaveStatus)}
            onClick={() => setSelected(rowId)}
            currentRowId={rowId}
          />
        );
      },
      width: '15%',
    },
  ];

  const handleEditCase = (rowId: string) => {
    const folderId = caseMap[rowId]?.item.folderId;
    dispatch(toggleCaseDrawer(folderId));
  };

  const handleDelete = (rowId: string) => {
    setSelected(rowId);
    setOpenCaseDeleteDialog(true);
  };

  const handleDeletionCancel = () => {
    setOpenCaseDeleteDialog(false);
  };

  const handleDeletionConfirm = () => {
    const deletedId = selected;
    dispatch(deleteCase(deletedId));
    try {
      const validCaseSdoIds = savePendingDeleteCaseToLocalStorage([deletedId]);
      setPendingDeleteIds(validCaseSdoIds.map((item) => item.value));
    } catch (e) {
      console.error('unable to save deleted case to local storage', e);
    }
    pollCasesPromiseRef.current?.abort();
    setSelected('');
    dispatch(searchCases({}));
    setOpenCaseDeleteDialog(false);
    pollCasesPromiseRef.current = dispatch(pollCases());
  };

  const caseActions: Action[] = [
    {
      action: intl.formatMessage({ id: 'editCase' }),
      icon: <WorkOutlineOutlinedIcon />,
      onClick: handleEditCase,
    },
    {
      action: intl.formatMessage({ id: 'addFiles' }),
      icon: <AddIcon />,
      onClick: handleUploadFile,
    },
    {
      action: intl.formatMessage({ id: 'delete' }),
      icon: <DeleteOutlineIcon />,
      onClick: handleDelete,
    },
  ];

  const renderEmptyState = () => (
    <TableRow>
      <TableCell>
        <EmptyState
          imageSrc={
            <img
              // TODO: Replace with a proper type for emptyCasesState - don't just declare empty module
              // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
              src={emptyCasesState}
              alt={intl.formatMessage({ id: 'emptyState' })}
              draggable={false}
            />
          }
          title={I18nTranslate.TranslateMessage('nothingFound')}
          description={I18nTranslate.TranslateMessage(
            'noCasesFoundDescription'
          )}
          buttonText={I18nTranslate.TranslateMessage('addNewCase')}
          onClick={() => dispatch(toggleCaseDrawer())}
        />
      </TableCell>
    </TableRow>
  );

  const isValidSortBy = (value: string): value is SortBy =>
    Object.values(SortBy).includes(value as SortBy);

  const handleSort = (key: string) => {
    if (!isValidSortBy(key)) {
      return;
    }

    let newSortDirection = sortDirection;
    if (sortBy === key) {
      newSortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    }

    dispatch(
      setSort({
        sortBy: key,
        sortDirection: newSortDirection,
      })
    );
    setSelected('');
  };

  const handleCaseFilterClick = () => {
    setOpenCaseFilter(true);
  };

  const handleCloseCaseFilter = () => {
    setOpenCaseFilter(false);
  };

  const handleApplyCaseFilter = (value: CaseFilterValue) => {
    if (!isEqual(caseFilter, value)) {
      dispatch(setCaseFilter(value));
    }
    setOpenCaseFilter(false);
  };
  const onPageChange = (
    _event: React.MouseEvent<HTMLButtonElement> | null,
    newPage: number
  ) => {
    dispatch(setOffset(newPage * limit));
    setSelected('');
  };

  const onRowsPerPageChange = (event: ChangeEvent<HTMLInputElement>) => {
    const oldPage = Math.floor(offset / limit);
    const newLimit = Number(event.target.value);
    let newOffset = oldPage * newLimit;

    // Ensure newOffset does not exceed total results
    if (newOffset >= totalResults) {
      newOffset = Math.floor(totalResults / newLimit) * newLimit;
    }

    dispatch(setOffset(newOffset));
    dispatch(setLimit(newLimit));
    setSelected('');
  };

  useEffect(() => {
    if (casesStatus === 'complete') {
      dispatch(checkCases());
    }
  }, [casesStatus]);

  useEffect(() => {
    pollCasesPromiseRef.current?.abort();

    if (folderContentTemplateSchemaId) {
      dispatch(
        searchCases({
          filteredStatusId: currentStatuses.map((status) => status.id),
        })
      );

      // Start polling
      pollCasesPromiseRef.current = dispatch(pollCases());
      pollStatusesTagsPromiseRef.current = dispatch(pollStatusesTags());
    }

    return () => {
      // Stop previous polling when a component unmounts or folder/offset/limit/... change
      pollCasesPromiseRef.current?.abort();
      pollStatusesTagsPromiseRef.current?.abort();
    };
  }, [
    dispatch,
    offset,
    sortBy,
    limit,
    sortDirection,
    folderContentTemplateSchemaId,
    caseFilter,
  ]);

  return (
    <div className="case-manager-table">
      <Table<Case>
        data={cases}
        dataMap={caseMap}
        columns={caseColumns}
        row={{
          selected,
          handleSelect,
          handleDoubleClick,
          pendingDeleteIds,
        }}
        sort={{
          orderBy: sortBy,
          direction: sortDirection,
          handleSort,
        }}
        pagination={{
          page: Math.floor(offset / limit),
          count: totalResults,
          rowsPerPage: limit,
          rowsPerPageOptions: [50, 100, 200, 300],
          onPageChange,
          onRowsPerPageChange,
        }}
        styles={{
          classname,
          isLoading: isTableLoading,
          emptyState: renderEmptyState(),
          isFixedTableLayout: true,
        }}
        actions={caseActions}
        onFilter={handleCaseFilterClick}
        extraProps={{
          pendingDeleteMessage: intl.formatMessage({ id: 'casePendingDelete' }),
        }}
        filterCount={appliedCaseFiltersCount(caseFilter)}
      />
      <Dialog
        open={openCaseDeleteDialog}
        title={intl.formatMessage({ id: 'areYouSure' })}
        onClose={handleDeletionCancel}
        onConfirm={handleDeletionConfirm}
        confirmText={intl.formatMessage({ id: 'yesDeleteCase' })}
        cancelText={intl.formatMessage({ id: 'cancel' })}
        isDelete
        disableConfirm={false}
      >
        {I18nTranslate.TranslateMessage('deleteCaseConfirmationMsg')}
      </Dialog>
      <Drawer open={openCaseFilter} anchor={'right'}>
        <CaseFilter
          filterValue={caseFilter}
          onClose={handleCloseCaseFilter}
          onApply={handleApplyCaseFilter}
        />
      </Drawer>
    </div>
  );
};

export default CaseTable;
