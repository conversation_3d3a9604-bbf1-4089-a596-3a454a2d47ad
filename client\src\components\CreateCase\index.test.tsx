import Drawer from '@components/Drawer';
import { CaseTag } from '@shared-types/types';
import { configureAppStore } from '@store/index';
import {
  initialState as caseManagerInitialState,
  CaseManagerSliceState,
} from '@store/modules/caseManager/slice';
import {
  initialState as settingsInitialState,
  SettingsSliceState,
} from '@store/modules/settings/slice';
import { act, fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CreateEditCaseDrawer from '.';
import { render } from '../../../test/render';

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: settingsInitialState,
  caseManager: caseManagerInitialState,
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

const mockCaseTags: CaseTag[] = [
  {
    id: '1',
    label: 'tag1',
    active: true,
  },
  {
    id: '2',
    label: 'tag2',
    active: true,
  },
  {
    id: '3',
    label: 'stolen',
    active: true,
  },
  {
    id: '4',
    label: 'lost',
    active: true,
  },
];

describe('Create Case', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the CreateEditCaseDialog', () => {
    const { container } = render(
      <BrowserRouter>
        <Drawer
          open
          onClose={() => {}}
          width={600}
          anchor={'right'}
          persistent={false}
        >
          <CreateEditCaseDrawer />
        </Drawer>
      </BrowserRouter>
    );

    expect(container).toBeTruthy();
    expect(screen.getByText('Case Name *')).toBeTruthy();
  });

  it('renders the save button as disabled when required fields are not filled', () => {
    render(
      <BrowserRouter>
        <Drawer
          open
          onClose={() => {}}
          width={600}
          anchor={'right'}
          persistent={false}
        >
          <CreateEditCaseDrawer />
        </Drawer>
      </BrowserRouter>
    );

    const input: HTMLInputElement = screen.getByTestId('case-name-textfield');
    expect(input).toHaveValue('');

    const saveButton = screen.getByText('Create Case');
    expect(saveButton).toBeDisabled();
  });

  it('renders the save button as enabled when required fields are filled', async () => {
    render(
      <BrowserRouter>
        <Drawer
          open
          onClose={() => {}}
          width={600}
          anchor={'right'}
          persistent={false}
        >
          <CreateEditCaseDrawer />
        </Drawer>
      </BrowserRouter>
    );

    //  Add case name
    const caseNameInput: HTMLInputElement = screen.getByTestId(
      'case-name-textfield'
    );

    act(() => {
      userEvent.type(caseNameInput, 'Case Name');
    });

    //  Add case id
    const caseIdInput: HTMLInputElement =
      screen.getByTestId('case-id-textfield');

    act(() => {
      userEvent.type(caseIdInput, 'CASEID-12345567');
    });

    // Save button should be enabled
    await waitFor(() => {
      const saveButton = screen.getByText('Create Case');
      expect(saveButton).toBeEnabled();
    });

    //  Clear the caseName input
    act(() => {
      userEvent.clear(caseNameInput);
    });

    // Create Case Button should once again be disabled
    await waitFor(() => {
      const saveButton = screen.getByText('Create Case');
      expect(saveButton).toBeDisabled();
    });
  });

  it('restricts the case name and id inputs to alphanumeric, underscores, dashes, spaces and apostrophes', async () => {
    render(
      <BrowserRouter>
        <Drawer
          open
          onClose={() => {}}
          width={600}
          anchor={'right'}
          persistent={false}
        >
          <CreateEditCaseDrawer />
        </Drawer>
      </BrowserRouter>
    );

    //  Add case name
    const caseNameInput: HTMLInputElement = screen.getByTestId(
      'case-name-textfield'
    );

    const submitButton = screen.getByTestId('create-edit-case-save-button');

    act(() => {
      userEvent.type(caseNameInput, 'Case Name');
    });

    await waitFor(() => {
      expect(caseNameInput.value).toBe('Case Name');
    });

    act(() => {
      userEvent.clear(caseNameInput);
      userEvent.type(caseNameInput, "Ca'se Nam'e");
    });

    await waitFor(() => {
      expect(caseNameInput.value).toBe("Ca'se Nam'e");
    });

    // Type in some special characters to input and submit
    act(() => {
      userEvent.type(caseNameInput, '&^$%');
    });
    act(() => {
      userEvent.click(submitButton);
    });
    await waitFor(() => {
      expect(
        screen.getByText("Only letters, numbers, _ , '\ and - allowed")
      ).toBeInTheDocument();
    });

    const caseIdInput: HTMLInputElement =
      screen.getByTestId('case-id-textfield');

    act(() => {
      userEvent.type(caseIdInput, 'CASEID-12345567');
    });

    await waitFor(() => {
      expect(caseIdInput.value).toBe('CASEID-12345567');
    });

    // Type in some special characters
    act(() => {
      userEvent.type(caseIdInput, '&^$%');
    });

    // Show error when Special characters is added
    await waitFor(() => {
      expect(submitButton).toBeDisabled();
      expect(
        screen.getByText("Only letters, numbers, _ , '\ and - allowed")
      ).toBeInTheDocument();
    });
  });

  it('should render case tags select and behave correctly on clicks', () => {
    const store = configureAppStore({
      ...initialStateForMock,
      settings: {
        ...initialStateForMock.settings,
        fetchedTags: {
          status: 'idle',
          error: '',
          tags: mockCaseTags,
        },
      },
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <Drawer
            open
            onClose={() => {}}
            width={600}
            anchor={'right'}
            persistent={false}
          >
            <CreateEditCaseDrawer />
          </Drawer>
        </BrowserRouter>
      </Provider>
    );

    expect(screen.queryByText('stolen')).not.toBeInTheDocument();

    //  Open the menu
    const selectMenus = screen.getAllByRole('combobox');
    act(() => {
      userEvent.click(selectMenus[1]);
    });

    waitFor(() => {
      //  Test case tags options are there
      expect(screen.getByText('stolen')).toBeInTheDocument();
      expect(screen.getByText('lost')).toBeInTheDocument();
    });

    // Select stolen tag
    const stolenOption = screen.getByTestId(
      'multi-select-search-option-stolen'
    );
    act(() => {
      userEvent.click(stolenOption);
    });
    waitFor(() => {
      // The 'stolen' tag should be selected in the list
      expect(stolenOption).toHaveAttribute('aria-selected', 'true');

      // The 'lost' tag should not be selected in the list
      const lostOption = screen.getByTestId('multi-select-search-option-lost');
      expect(lostOption).not.toHaveAttribute('aria-selected', 'true');
    });
  });

  it('should show discard changes dialog when field changed and click cancel button', async () => {
    render(
      <BrowserRouter>
        <Drawer
          open
          onClose={() => {}}
          width={600}
          anchor={'right'}
          persistent={false}
        >
          <CreateEditCaseDrawer />
        </Drawer>
      </BrowserRouter>
    );
    const inputs = screen.getAllByTestId('case-description-textfield');
    userEvent.type(inputs[0], 'Case Test ');

    const cancelButton = screen.getByTestId('create-edit-case-cancel-button');
    userEvent.click(cancelButton);
    await waitFor(() => {
      expect(
        screen.getByText('Are you sure you want to discard your changes?')
      ).toBeInTheDocument();
    });
  });

  it('should display character count when typing in description field', () => {
    const store = configureAppStore({
      ...initialStateForMock,
    });
    render(
      <Provider store={store}>
        <BrowserRouter>
          <Drawer
            open
            onClose={() => {}}
            width={600}
            anchor={'right'}
            persistent={false}
          >
            <CreateEditCaseDrawer />
          </Drawer>
        </BrowserRouter>
      </Provider>
    );
    const descriptionField = screen.getByTestId('case-description-textfield');
    const characterCount = screen.getByTestId('description-count');

    expect(characterCount).toBeInTheDocument();

    fireEvent.change(descriptionField, {
      target: { value: 'Test description' },
    });
    expect(screen.getByText('16')).toBeInTheDocument();

    // Make sure that the description field has a maxlength attribute,
    // Using the native maxlength HTML attribute is otherwise untestable, so we just make sure its there.
    waitFor(() => {
      expect(descriptionField.outerHTML).toContain('maxlength="1000"');
    });
  });

  it('should disable all fields and cancel and create case button when creating a new case', () => {
    const store = configureAppStore({
      ...initialStateForMock,
      caseManager: {
        ...initialStateForMock.caseManager,
        createCase: {
          ...initialStateForMock.caseManager.createCase,
          status: 'loading',
        },
      },
    });

    render(
      <Provider store={store}>
        <BrowserRouter>
          <Drawer
            open
            onClose={() => {}}
            width={600}
            anchor={'right'}
            persistent={false}
          >
            <CreateEditCaseDrawer />
          </Drawer>
        </BrowserRouter>
      </Provider>
    );

    expect(screen.getByTestId('case-name-textfield')).toBeDisabled();
    expect(screen.getByTestId('case-id-textfield')).toBeDisabled();
    expect(screen.getByTestId('case-description-textfield')).toBeDisabled();
    expect(
      screen.getByTestId('case-status-list').querySelector('input,select')
    ).toBeDisabled();
    expect(
      screen.getByTestId('date-selector-form').querySelector('input,select')
    ).toBeDisabled();
    expect(
      screen.getByTestId('multi-select-list').querySelector('input,select')
    ).toBeDisabled();
    expect(screen.getByTestId('create-edit-case-save-button')).toBeDisabled();
    expect(screen.getByTestId('create-edit-case-cancel-button')).toBeDisabled();
  });
});
