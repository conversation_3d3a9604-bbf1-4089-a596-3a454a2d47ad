.Sdk-MuiTableContainer-root {
  display: flex;
  border-radius: 8px;
  flex-direction: column;
  background-color: var(--background-secondary);
  outline: none;
}

.Sdk-MuiTable-root {
  //height: 200px;
  overflow: scroll;
}

.table-container-fixed-layout {
  table-layout: fixed;
  min-width: 700px;
}

.case-table-cell {
  position: relative;
  width: 100%;

  &::before {
    content: '&nbsp;';
    visibility: hidden;
  }

  & > span {
    position: absolute;
    left: 0;
    right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.Sdk-MuiTableHead-root {
  top: 0;
  position: sticky;
  background: var(--background-secondary);
  box-shadow: inset 0 -1px 0 0 var(--border-color);
  -webkit-box-shadow: inset 0 -1px 0 0 var(--border-color);
  z-index: 1;
}

.Sdk-MuiTableBody-root {
  flex: 1;
  overflow: auto;
}

.Sdk-MuiCheckbox-root {
  padding: 0;
}

.Sdk-MuiTableCell-root, .table-cell {
  padding: 16px;
  font-size: 14px;
  font-weight: 300;
  cursor: pointer;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);

  & > SVG > path {
    fill: var(--text-primary);
  }
}

.Sdk-MuiTableHead-root {
  .Sdk-MuiTableCell-root {
    font-weight: 500;
  }
}

.Sdk-MuiTablePagination-root {
  overflow: unset;
  border-top: 1px solid var(--table-border-color);
}

.Sdk-MuiTablePagination-spacer {
  flex: 0;
}

.table-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.table-container-empty {
  height: -webkit-fill-available;
}

.table-loader {
  position: relative;

  & > span {
    position: absolute;
    top: calc(50% - 20px);
    left: calc(50% - 20px);
    width: 40px !important;
    height: 40px !important;
  }
}

.empty-table {
  height: 70vh;
  position: relative;
}

.grouped-view {
  .empty-table {
    height: 30vh;
    position: relative;
  }
}

.body-row:hover:not(.disable-row) {
  background: var(--background-selected);
  box-shadow: inset 3px 0 0 0 var(--button-dark-blue);
  -webkit-box-shadow: inset 3px 0 0 0 var(--button-dark-blue);
}

.body-row {
  &:hover,
  &:focus {
    .menu-cell {
      & > button {
        visibility: visible;
      }
    }
  }

  .menu-cell {
    display: flex;
    justify-content: flex-end;
    flex: 1;
    padding: 12px 20px 12px 0;

    & > button {
      color: var(--text-primary);
      visibility: hidden;

      &:hover {
        color: var(--text-primary);
      }
    }
  }

  .menu-cell.select-row-menu {
    & > button {
      visibility: visible;
    }
  }

  .menu-cell.select-row-menu-hidden {
    & > button {
      visibility: hidden;
    }
  }

  display: flex;
  outline: 0px;
  width: 100%;
  cursor: pointer;
}

.disable-row {
  opacity: 0.5;

  input[type='checkbox'] {
    pointer-events: none;
  }
}

.filter-list-badge-background {
  background-color: #A61111;
  padding-top: 2px;
}

.filter-list-badge-color {
  color: var(--button-inner);
}

.selected {
  background: var(--background-selected);
  box-shadow: inset 3px 0 0 0 var(--button-dark-blue);
  -webkit-box-shadow: inset 3px 0 0 0 var(--button-dark-blue);

  .menu-cell {
    & > button {
      visibility: visible;
    }
  }
}
