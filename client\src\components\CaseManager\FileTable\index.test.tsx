import { configureAppStore } from '@store/index';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
} from '@store/modules/caseDetail/slice';
import { toggleOpenEditDrawer } from '@store/modules/metadata/slice';
import { fireEvent, screen, waitFor, within } from '@testing-library/dom';
import { act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@theme';
import { GQLApi } from '@utils/helpers';
import {
  dispatchCustomEvent,
  eventHistory,
} from '@utils/helpers/dispatchCustomEvent';
import { ViewType } from '@utils/local-storage/viewTypes';
import { clone } from 'lodash';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import { describe, expect, it, vi } from 'vitest';
import FileTable from '.';
import { render } from '../../../../test/render';
import {
  SearchSliceState,
  initialState as searchInitialState,
} from '@store/modules/search/slice.ts';
import { SearchMediaResponse } from '@store/modules/search/searchFiles.ts';

const mockFiles = [
  {
    id: 'file_1',
    fileName: 'file1',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user1',
    createdTime: '2021-09-01T00:00:00Z',
    updatedTime: '2021-09-01T00:00:00Z',
    description: '',
    duration: 22.5,
  },
  {
    id: 'file_2',
    fileName: 'file2',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user2',
    createdTime: '2021-09-02T00:00:00Z',
    updatedTime: '2021-09-02T00:00:00Z',
    description: '',
    duration: 22.5,
  },
  {
    id: 'file_3',
    fileName: 'file3',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user3',
    createdTime: '2021-09-03T00:00:00Z',
    updatedTime: '2021-09-03T00:00:00Z',
    description: '',
    duration: 22.5,
  },
  {
    id: 'file_4',
    fileName: 'file4',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user4',
    createdTime: '2021-09-01T00:00:00Z',
    updatedTime: '2021-09-01T00:00:00Z',
    description: '',
    duration: 22.5,
  },
  {
    id: 'file_5',
    fileName: 'file5',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user5',
    createdTime: '2021-09-01T00:00:00Z',
    updatedTime: '2021-09-01T00:00:00Z',
    description: '',
    duration: 22.5,
  },
];

const page1results: SearchMediaResponse = {
  searchMedia: {
    jsondata: {
      results: [
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '**********',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/**********',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '**********',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0, // Added missing property
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '22222222',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '22222222',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '33333333',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '33333333',
                mimetype: 'audio/mp4',
                segmented: true,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '444444444',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '444444444',
                mimetype: 'audio/mp4',
                segmented: true,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '555555555',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '555555555',
                mimetype: 'audio/mp4',
                segmented: true,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '66666666',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/22222222',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            parentTreeObjectIds: [],
            tags: [],
            creator: 'N/A',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '66666666',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
      ],
      totalResults: {
        value: 100,
        relation: '',
      },
      limit: 50,
      from: 0,
      to: 50,
      searchToken: '',
      timestamp: 0,
    },
  },
};

const initialState: {
  caseDetail: CaseDetailSliceState;
  search: SearchSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  caseDetail: {
    ...caseDetailInitialState,
    files: {
      status: 'complete',
      data: {
        results: mockFiles,
        totalResults: 50,
        limit: 50,
        from: 1,
        to: 50,
      },
    },
    viewType: ViewType.LIST,
  },
  search: {
    ...searchInitialState,
    searchFiles: {
      ...searchInitialState.searchFiles,
      searchParams: {
        searchResultType: 'ungrouped',
        checkedResultCategories: [],
        pagination: {
          ungrouped: {
            offset: 0,
            limit: 50,
          },
        },
      },
      ungroupedSearch: {
        status: 'success',
        data: page1results,
        pagination: {
          offset: 0,
          limit: 50,
        },
      },
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      evidenceTypeRegistryId: 'evidenceTypeRegistryId132',
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

const defaultProps = {
  selected: '',
  handleSelect: vi.fn(),
  handleDoubleClick: vi.fn(),
  handleUploadFile: vi.fn(),
  setSelectedFolderId: vi.fn(),
  selectedFolderId: '',
  setSelected: vi.fn(),
  pendingDeleteIds: [],
  setPendingDeleteIds: vi.fn(),
  pendingMoveFileIds: [],
};

const getCheckbox = (dataTestId: string) =>
  within(screen.getByTestId(dataTestId)).getByRole<HTMLInputElement>(
    'checkbox'
  );

describe('File Table', () => {
  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () => [
        {
          index: 0,
          size: 69,
          start: 0,
          end: 69,
          key: 0,
          measureElement: vi.fn(),
        },
        {
          index: 1,
          size: 69,
          start: 69,
          end: 138,
          key: 1,
          measureElement: vi.fn(),
        },
        {
          index: 2,
          size: 69,
          start: 138,
          end: 207,
          key: 2,
          measureElement: vi.fn(),
        },
        {
          index: 3,
          size: 69,
          start: 207,
          end: 276,
          key: 3,
          measureElement: vi.fn(),
        },
        {
          index: 4,
          size: 69,
          start: 276,
          end: 345,
          key: 4,
          measureElement: vi.fn(),
        },
      ],
      getTotalSize: () => 69,
      measure: vi.fn(),
      scrollToIndex: vi.fn(),
    })),
  }));

  it('should render menu action correctly', async () => {
    const store = configureAppStore(initialState);
    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    const firstRowActionIcon = screen.getAllByTestId('search-table-menu')[0];

    userEvent.click(firstRowActionIcon);
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });

    userEvent.click(screen.getByText('Edit Metadata'));
    expect(store.dispatch).toHaveBeenCalledWith(
      toggleOpenEditDrawer(
        page1results.searchMedia.jsondata.results[0].recording.recordingId
      )
    );
  });

  it('shows grid view files when viewType is set to GRID', async () => {
    const gridState = clone({
      ...initialState,
      caseDetail: { ...initialState.caseDetail, viewType: ViewType.GRID },
    });
    const store = configureAppStore(gridState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.queryAllByTestId('file-card')[0]).toBeInTheDocument();
    });
  });

  it('shows list view files when viewType is set to LIST', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.queryByTestId('file-card')).not.toBeInTheDocument();
    });
  });

  it('should blur file cards when the blur switch is toggled and the viewType is GRID', async () => {
    const gridState = clone({
      ...initialState,
      caseDetail: { ...initialState.caseDetail, viewType: ViewType.GRID },
    });
    const store = configureAppStore(gridState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    const blurSwitch = screen
      .getAllByTestId('blur-switch')[0]
      .querySelector('input');
    if (!blurSwitch) {
      throw new Error('Blur switch not found');
    }
    expect(blurSwitch).toBeInTheDocument();
    expect(blurSwitch?.checked).toBe(false);

    act(() => {
      blurSwitch.click();
    });

    expect(blurSwitch?.checked).toBe(true);
    expect(localStorage.getItem('investigate-set-blur')).toBe('true');

    await waitFor(() => {
      expect(
        screen.getAllByTestId('file-card-image')[0].children[0]
      ).toHaveClass('blurred');
    });
  });

  it('should render circular progress when reload page', () => {
    const store = configureAppStore({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
      },
      search: {
        ...initialState.search,
        searchFiles: {
          ...initialState.search.searchFiles,
          searchParams: {
            searchResultType: 'ungrouped',
            checkedResultCategories: [],
            pagination: {
              ungrouped: {
                offset: 0,
                limit: 50,
              },
            },
          },
          ungroupedSearch: {
            status: 'loading',
            data: {
              searchMedia: {
                jsondata: {
                  results: [],
                  totalResults: {
                    value: 0,
                    relation: '',
                  },
                  limit: 50,
                  from: 0,
                  to: 0,
                  searchToken: '',
                  timestamp: 0,
                },
              },
            } as SearchMediaResponse,
            totalResults: 0,
            pagination: {
              offset: 0,
              limit: 50,
            },
          },
        },
      },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('table-body_loading-icon')).toBeInTheDocument();
  });

  it('should show bulk delete dialog after event received and confirm delete', async () => {
    const getTdoDetailsSpy = vi
      .spyOn(GQLApi.prototype, 'getTdoDetails')
      .mockImplementationOnce(() =>
        Promise.resolve({
          tags: [],
          veritoneFile: {
            fileType: 'image',
          },
        })
      );

    const softDeleteFileSpy = vi
      .spyOn(GQLApi.prototype, 'softDeleteFile')
      .mockImplementationOnce(() =>
        Promise.resolve({
          folder: {
            id: 'folder_1',
            name: 'Mock Folder',
            description: 'Mock folder description',
            treeObjectId: 'tree_object_1',
            createdDateTime: '2021-09-01T00:00:00Z',
            modifiedDateTime: '2021-09-01T00:00:00Z',
            contentTemplates: [],
          },
        })
      );

    const store = configureAppStore(initialState);
    render(
      <ThemeProvider>
        <Provider store={store}>
          <MemoryRouter>
            <FileTable {...defaultProps} />
          </MemoryRouter>
        </Provider>
      </ThemeProvider>
    );

    act(() => {
      getCheckbox('check-box__check-row-**********').click();
    });

    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-**********')).toBeChecked();
    });

    act(() => {
      dispatchCustomEvent('delete-all-selected-files');
    });

    await waitFor(() => {
      const events = eventHistory.map(({ eventName }) => eventName);
      expect(events).toContain('delete-all-selected-files');
    });

    await waitFor(() => {
      expect(screen.queryByTestId('dialog')).toBeInTheDocument();

      const dialogElement = screen.getByTestId('dialog');

      expect(dialogElement).toHaveTextContent(
        'Deleting will immediately remove content from your Organization'
      );
    });

    act(() => {
      fireEvent.change(screen.getByTestId('type-confirmation-input'), {
        target: { value: 'delete-file' },
      });
    });

    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).not.toBeDisabled();
    });

    act(() => {
      screen.getByTestId('confirm-button')?.click();
    });

    await waitFor(() => {
      expect(getTdoDetailsSpy).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(softDeleteFileSpy).toHaveBeenCalled();
    });
  });

  it('should show bulk move dialog after event received', async () => {
    const store = configureAppStore(initialState);
    render(
      <ThemeProvider>
        <Provider store={store}>
          <MemoryRouter>
            <FileTable {...defaultProps} />
          </MemoryRouter>
        </Provider>
      </ThemeProvider>
    );

    act(() => {
      getCheckbox('check-box__check-row-**********').click();
    });

    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-**********')).toBeChecked();
    });

    act(() => {
      dispatchCustomEvent('move-all-selected-files');
    });

    await waitFor(() => {
      const events = eventHistory.map(({ eventName }) => eventName);
      expect(events).toContain('move-all-selected-files');
    });

    await waitFor(() => {
      expect(screen.queryByTestId('move-file-dialog')).toBeInTheDocument();
    });

    const dialogElement = screen.getByTestId('move-file-dialog');

    expect(dialogElement).toHaveTextContent('Move to Case');
  });

  it('should render CircularProgress when loading in GRID view', async () => {
    const gridState = clone({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
        viewType: ViewType.GRID,
      },
      search: {
        ...initialState.search,
        searchFiles: {
          ...initialState.search.searchFiles,
          searchParams: {
            searchResultType: 'ungrouped',
            checkedResultCategories: [],
            pagination: {
              ungrouped: {
                offset: 0,
                limit: 50,
              },
            },
          },
          ungroupedSearch: {
            status: 'loading',
            data: {
              searchMedia: {
                jsondata: {
                  results: [],
                  totalResults: {
                    value: 0,
                    relation: '',
                  },
                  limit: 50,
                  from: 0,
                  to: 0,
                  searchToken: '',
                  timestamp: 0,
                },
              },
            } as SearchMediaResponse,
            totalResults: 0,
            pagination: {
              offset: 0,
              limit: 50,
            },
          },
        },
      },
    });
    const store = configureAppStore(gridState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(
        screen.getByTestId('file-table__card-view-loading')
      ).toBeInTheDocument();
    });
  });

  it('should select a row when clicking checkbox', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const checkbox = getCheckbox('check-box__check-row-**********');
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(checkbox).toBeChecked();
    });
  });

  it('should select multiple rows with Shift key', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    ['**********', '22222222', '33333333', '444444444', '555555555'].forEach(
      (id) => {
        expect(getCheckbox(`check-box__check-row-${id}`)).not.toBeChecked();
      }
    );
    fireEvent.click(getCheckbox('check-box__check-row-**********'));
    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-**********')).toBeChecked();
    });
    fireEvent.click(getCheckbox('check-box__check-row-555555555'), {
      shiftKey: true,
    });
    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-555555555')).toBeChecked();
    });
    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-**********')).toBeChecked();
      expect(getCheckbox('check-box__check-row-22222222')).toBeChecked();
      expect(getCheckbox('check-box__check-row-33333333')).toBeChecked();
      expect(getCheckbox('check-box__check-row-444444444')).toBeChecked();
      expect(getCheckbox('check-box__check-row-555555555')).toBeChecked();
    });
  });

  it('should unselect a row when unchecking its checkbox in file table', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    const checkbox = getCheckbox('check-box__check-row-**********');
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(checkbox).not.toBeChecked();
    });
  });
});
