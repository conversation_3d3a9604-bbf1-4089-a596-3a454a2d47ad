export const mockSearchMediaResponse = {
  searchMedia: {
    jsondata: {
      totalResults: {
        value: 3,
      },
      results: [
        {
          recording: {
            recordingId: '3480027835',
            parentTreeObjectIds: ['dae500ca-d7c5-46d4-a983-7f87115e66c3'],
            createdTime: '2025-02-07T06:14:03.000Z',
            modifiedTime: '2025-02-07T06:14:03.000Z',
            programLiveImage: undefined,
          },
          context: [
            {
              'veritone-file': {
                createdbyname: 'name',
                filename: 'Test',
                filetype: 'text/plain',
              },
            },
            {
              'veritone-file': {
                createdbyname: 'name',
                filename: 'spanish.txt',
                filetype: 'text/plain',
              },
            },
          ],
        },
        {
          recording: {
            recordingId: '3480027613',
            parentTreeObjectIds: ['dae500ca-d7c5-46d4-a983-7f87115e66c3'],
            createdTime: '2025-02-07T05:19:16.000Z',
            modifiedTime: '2025-02-07T05:19:36.000Z',
            programLiveImage: undefined,
          },
          context: [
            {
              'veritone-file': {
                createdbyname: 'name',
                filename: 'mountain.jpeg',
                filetype: 'image/jpeg',
              },
            },
            {
              'veritone-file': {
                createdbyname: 'name',
                filename: 'mountain.jpeg',
                filetype: 'image/jpeg',
              },
            },
          ],
        },
        {
          recording: {
            recordingId: '3480027615',
            parentTreeObjectIds: ['dae500ca-d7c5-46d4-a983-7f87115e66c3'],
            createdTime: '2025-02-07T05:19:23.000Z',
            modifiedTime: '2025-02-07T05:19:56.000Z',
            programLiveImage: undefined,
          },
          context: [
            {
              'veritone-file': {
                createdbyname: 'name',
                filename: 'Shoplifter.mp4',
                filetype: 'video/mp4',
                duration: 120.5,
              },
            },
            {
              'veritone-file': {
                createdbyname: 'name',
                filename: 'Shoplifter.mp4',
                filetype: 'video/mp4',
                duration: 120.5,
              },
            },
          ],
        },
      ],
      from: 0,
      limit: 20,
      to: 20,
    },
  },
};

export const mockGetFolderResponse = {
  id: 'folderId123',
  name: 'folderName123',
  ownerId: 'ownerId123',
  treeObjectId: 'treeObjectId123',
  createdDateTime: 'createdDateTime123',
  modifiedDateTime: 'modifiedDateTime123',
  contentTemplates: [],
};

export const searchByNameAndSortByDate = {
  gqlEndpoint: 'apiroot123/graphQLEndpoint123',
  query: `
      query searchMedia($search: JSONData!) {
        searchMedia(search: $search) {
          jsondata
        }
      }
    `,
  token: 'sessionToken123',
  variables: {
    search: {
      index: ['mine'],
      type: 'file',
      select: ['veritone-file'],
      limit: 10,
      offset: 0,
      sort: [{ field: 'createdTime', order: 'desc' }],
      query: {
        conditions: [
          // {
          //   operator: 'query_object',
          //   field: 'tags',
          //   not: false,
          //   query: {
          //     operator: 'term',
          //     field: 'tags.value',
          //     value: 'veritone_investigate',
          //     dotNotation: true,
          //   },
          // },
          {
            operator: 'and',
            conditions: [
              {
                field: 'veritone-file.filename',
                operator: 'query_string',
                value: `*Test*`,
              },
            ],
          },
          {
            field: 'parentTreeObjectIds',
            operator: 'terms',
            values: ['treeObjectId123'],
          },
          {
            operator: 'query_object',
            field: 'tags',
            not: true,
            query: {
              operator: 'range',
              field: 'tags.value',
              gte: '2024-01-01T00:00:00.000Z',
              dotNotation: true,
            },
          },
        ],
        operator: 'and',
      },
    },
  },
  veritoneAppId: 'veritoneAppId123',
};

export const newCaseDetailState = {
  data: {
    from: 0,
    limit: 10,
    results: [
      {
        createdByName: 'name',
        createdTime: '2025-02-07T06:14:03.000Z',
        fileName: 'Test',
        fileType: 'text/plain',
        id: '3480027835',
        parentTreeObjectIds: ['dae500ca-d7c5-46d4-a983-7f87115e66c3'],
        updatedTime: '2025-02-07T06:14:03.000Z',
        programLiveImage: undefined,
        description: '',
        duration: -1,
      },
      {
        createdByName: 'name',
        createdTime: '2025-02-07T05:19:16.000Z',
        fileName: 'mountain.jpeg',
        fileType: 'image/jpeg',
        id: '3480027613',
        parentTreeObjectIds: ['dae500ca-d7c5-46d4-a983-7f87115e66c3'],
        updatedTime: '2025-02-07T05:19:36.000Z',
        programLiveImage: undefined,
        description: '',
        duration: -1,
      },
      {
        createdByName: 'name',
        createdTime: '2025-02-07T05:19:23.000Z',
        fileName: 'Shoplifter.mp4',
        fileType: 'video/mp4',
        id: '3480027615',
        parentTreeObjectIds: ['dae500ca-d7c5-46d4-a983-7f87115e66c3'],
        updatedTime: '2025-02-07T05:19:56.000Z',
        programLiveImage: undefined,
        description: '',
        duration: 120.5,
      },
    ],
    to: 20,
    totalResults: 3,
  },
  error: '',
  status: 'complete',
};
