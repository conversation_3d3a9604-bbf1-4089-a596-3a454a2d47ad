RC_URL=registry.central.aiware.com

# If git commit is not set, get it from FS
ifeq ($(GIT_COMMIT),)
	GIT_COMMIT=$(shell git rev-parse HEAD)
endif

build: build-investigate build-investigate-k8s

run: run-investigate run-k8s

build-investigate:
	docker build -t investigate-app:${GIT_COMMIT} -f ./Dockerfile --build-arg K8S_BUILD=FALSE --build-arg APPLICATION=investigate-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag investigate-app:${GIT_COMMIT} investigate-app:latest

build-investigate-k8s:
	docker build -t investigate-k8s-app:${GIT_COMMIT} -f ./Dockerfile --build-arg K8S_BUILD=TRUE --build-arg APPLICATION=investigate-k8s-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag investigate-k8s-app:${GIT_COMMIT} investigate-k8s-app:latest
	docker tag investigate-k8s-app:${GIT_COMMIT} investigate-app:k8s

run-investigate:
	docker run -p 9000:9000 -e ENVIRONMENT=stage -e APPLICATION=investigate-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} investigate-app 

run-k8s:
	docker run -p 9004:9004 -e API_HOST=https://veritone.com -e ENVIRONMENT=stage -e APPLICATION=investigate-k8s-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} investigate-k8s-app:latest

test: build-investigate-k8s run-k8s

publish: publish-investigate publish-investigate-k8s

publish-investigate:
	$(MAKE) docker-tag SERVICE=investigate-app
	$(MAKE) docker-push SERVICE=investigate-app

publish-investigate-k8s:
	$(MAKE) docker-tag SERVICE=investigate-k8s-app
	$(MAKE) docker-push SERVICE=investigate-k8s-app

docker-tag:
	echo "Tagging ${SERVICE} with Registry Central registry ${RC_URL}"
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:latest
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:dev

docker-push:
	echo "Pushing ${SERVICE} to ${RC_URL}"
	docker push ${RC_URL}/${SERVICE}:latest
	docker push ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker push ${RC_URL}/${SERVICE}:dev
